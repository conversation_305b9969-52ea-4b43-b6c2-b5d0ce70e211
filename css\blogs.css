@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

:root {
  --color-bg: #f8f6f3;
  --color-primary: #b85c19;
  --color-secondary: #f2c572;
  --color-card: #fff;
  --color-text: #2d3a3a;
  --color-muted: #8a8a8a;
  --color-accent: #f7e7d3;
  --shadow: 0 4px 24px 0 rgba(184,92,25,0.08);
  --radius: 18px;
  --transition: 0.3s cubic-bezier(.4,2,.6,1);
}

body {
  font-family: 'Poppins', Arial, sans-serif;
  background: var(--color-bg);
  color: var(--color-text);
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

.blogs-hero {
  background: linear-gradient(120deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: var(--color-card);
  text-align: center;
  padding: 3rem 1rem 2rem 1rem;
  border-radius: 0 0 var(--radius) var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 2.5rem;
}
.blogs-hero h1 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.7rem;
  letter-spacing: 1px;
}
.blogs-hero p {
  font-size: 1.15rem;
  margin: 0;
  opacity: 0.95;
}

.blogs-list-container {
  max-width: 1200px;
  margin: 0 auto 3rem auto;
  padding: 0 1.5rem;
}
.blogs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.blog-card {
  background: var(--color-card);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: box-shadow var(--transition), transform var(--transition);
  min-height: 420px;
  position: relative;
}
.blog-card:hover {
  box-shadow: 0 10px 36px 0 rgba(184,92,25,0.16);
  transform: translateY(-4px) scale(1.02);
}
.blog-image {
  width: 100%;
  height: 210px;
  overflow: hidden;
  background: var(--color-accent);
  display: flex;
  align-items: center;
  justify-content: center;
}
.blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(.4,2,.6,1);
}
.blog-card:hover .blog-image img {
  transform: scale(1.07);
}
.blog-content {
  padding: 1.3rem 1.2rem 1.2rem 1.2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}
.blog-content h2 {
  font-size: 1.3rem;
  margin: 0 0 0.5rem 0;
  color: var(--color-primary);
  font-weight: 600;
}
.blog-date {
  font-size: 0.98rem;
  color: var(--color-muted);
  margin-bottom: 0.7rem;
}
.blog-excerpt {
  font-size: 1.05rem;
  color: var(--color-text);
  margin-bottom: 1.2rem;
  flex: 1;
}
.read-more-btn {
  display: inline-block;
  background: var(--color-primary);
  color: var(--color-card);
  padding: 0.6rem 1.3rem;
  border-radius: var(--radius);
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: 0.5px;
  transition: background var(--transition), color var(--transition);
  margin-top: auto;
  box-shadow: 0 2px 8px 0 rgba(184,92,25,0.10);
}
.read-more-btn:hover {
  background: var(--color-secondary);
  color: var(--color-primary);
}

/* Single Blog Post Styles */
.blogpost-container {
  max-width: 800px;
  margin: 2.5rem auto 3rem auto;
  padding: 0 1.5rem;
}
.back-btn {
  display: inline-block;
  margin-bottom: 1.5rem;
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 1.05rem;
  transition: color var(--transition);
}
.back-btn:hover {
  color: var(--color-secondary);
  text-decoration: underline;
}
#blog-post {
  background: var(--color-card);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 2.2rem 2rem 2rem 2rem;
  animation: fadeInUp 0.8s cubic-bezier(.4,2,.6,1);
}
#blog-post .blog-image {
  width: 100%;
  height: 320px;
  margin-bottom: 1.5rem;
  border-radius: var(--radius);
  overflow: hidden;
  background: var(--color-accent);
  display: flex;
  align-items: center;
  justify-content: center;
}
#blog-post .blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
#blog-post h1 {
  font-size: 2rem;
  color: var(--color-primary);
  margin: 0 0 0.7rem 0;
  font-weight: 600;
}
#blog-post .blog-date {
  font-size: 1.05rem;
  color: var(--color-muted);
  margin-bottom: 1.2rem;
}
#blog-post .blog-content {
  font-size: 1.13rem;
  color: var(--color-text);
  line-height: 1.7;
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .blogs-grid {
    grid-template-columns: 1fr;
    gap: 1.3rem;
  }
  .blogpost-container {
    padding: 0 0.5rem;
  }
  #blog-post {
    padding: 1.2rem 0.7rem 1.2rem 0.7rem;
  }
  #blog-post .blog-image {
    height: 180px;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: none;
  }
} 