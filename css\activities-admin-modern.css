/* Modern Activities Admin CSS */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Modern color palette */
  --primary: #4361ee;
  --primary-light: #4895ef;
  --primary-dark: #3a0ca3;
  --secondary: #f72585;
  --success: #4cc9f0;
  --warning: #f8961e;
  --danger: #f94144;
  --light: #f8f9fa;
  --dark: #212529;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(67, 97, 238, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
}

/* Base styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-light);
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  padding-bottom: var(--spacing-6);
}

.admin-container {
  padding: var(--spacing-6);
  padding-top: calc(var(--spacing-6) + 60px); /* Add space for header height */
  max-width: 1400px;
  margin: 0 auto;
  margin-left: 260px; /* Match sidebar width */
  animation: fadeIn 0.5s ease-out;
  width: calc(100% - 260px); /* Adjust width to account for sidebar */
  transition: margin-left 0.3s ease, width 0.3s ease; /* Smooth transition when sidebar changes */
  position: relative;
  min-height: 100vh;
}

/* Adjust container when sidebar is in small mode */
.sidebar.small ~ .admin-container {
  margin-left: 70px; /* Match small sidebar width */
  width: calc(100% - 70px);
}

/* Ensure proper spacing for tablets */
@media (min-width: 481px) and (max-width: 1024px) {
  .sidebar.small ~ .admin-container {
    margin-left: 70px;
    width: calc(100% - 70px);
  }
}

/* Adjust container when sidebar is hidden */
.sidebar.hide ~ .admin-container {
  margin-left: 0;
  width: 100%;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Typography */
h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-6);
  position: relative;
  padding-bottom: var(--spacing-4);
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: var(--radius-full);
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-4);
}

/* Alerts */
.alert {
  padding: var(--spacing-4) var(--spacing-5);
  margin-bottom: var(--spacing-6);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  animation: slideInDown 0.4s ease-out;
}

@keyframes slideInDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOutUp {
  from { transform: translateY(0); opacity: 1; }
  to { transform: translateY(-20px); opacity: 0; }
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.alert-success {
  background-color: rgba(76, 201, 240, 0.1);
  border: 1px solid rgba(76, 201, 240, 0.2);
  color: var(--gray-800);
}

.alert-success::before {
  background-color: var(--success);
}

.alert-danger {
  background-color: rgba(249, 65, 68, 0.1);
  border: 1px solid rgba(249, 65, 68, 0.2);
  color: var(--gray-800);
}

.alert-danger::before {
  background-color: var(--danger);
}

/* Section styling */
.form-section {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-8);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.form-section:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
}

/* Buttons */
.btn-add {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  padding: var(--spacing-3) var(--spacing-5);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.btn-add:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

.btn-add i {
  font-size: 0.9rem;
}

.btn-edit, .btn-delete {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn-edit {
  background-color: var(--primary-light);
  color: white;
}

.btn-delete {
  background-color: var(--danger);
  color: white;
}

.btn-edit:hover, .btn-delete:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-edit:hover::after, .btn-delete:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: inherit;
}

/* Activities grid */
.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-5);
}

/* Activity card */
.activity-card {
  position: relative;
  background-color: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  display: flex;
  flex-direction: column;
  height: 100%;
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeInUp {
  to { opacity: 1; transform: translateY(0); }
}

.activity-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.activity-card-image {
  height: 180px;
  overflow: hidden;
  position: relative;
}

.activity-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.activity-card:hover .activity-card-image img {
  transform: scale(1.05);
}

.activity-card-content {
  padding: var(--spacing-5);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.activity-card-content h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-3);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
}

.activity-card-content .content {
  color: var(--gray-700);
  margin-bottom: var(--spacing-4);
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-meta {
  display: flex;
  gap: var(--spacing-3);
  margin-top: auto;
}

.order-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-3);
  background-color: var(--gray-100);
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--gray-700);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(76, 201, 240, 0.1);
  color: var(--success);
}

.status-badge.inactive {
  background-color: rgba(173, 181, 189, 0.1);
  color: var(--gray-600);
}

.activity-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  background-color: var(--gray-100);
}

/* Inactive card styling */
.activity-card.inactive {
  opacity: 0.7;
}

.activity-card.inactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    45deg,
    rgba(0, 0, 0, 0.02),
    rgba(0, 0, 0, 0.02) 10px,
    rgba(0, 0, 0, 0.04) 10px,
    rgba(0, 0, 0, 0.04) 20px
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* No activities placeholder */
.no-activities {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-12);
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
}

.no-activities i {
  font-size: 4rem;
  margin-bottom: var(--spacing-6);
  color: var(--gray-400);
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.no-activities p {
  color: var(--gray-600);
  max-width: 400px;
  font-size: 1.1rem;
}

/* Modal styling */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999; /* Ensure modal appears above all other elements */
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.modal.show {
  opacity: 1;
}

.modal-content {
  background-color: white;
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-5) var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--gray-500);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.close-modal:hover {
  color: var(--gray-900);
}

.modal-body {
  padding: var(--spacing-6);
}

/* Form styling */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  color: var(--gray-800);
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group textarea {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 1rem;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.form-group input[type="file"] {
  display: block;
  width: 100%;
  padding: var(--spacing-3) 0;
}

.form-text {
  display: block;
  margin-top: var(--spacing-2);
  font-size: 0.85rem;
  color: var(--gray-600);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  accent-color: var(--primary);
}

.checkbox-group label {
  margin: 0;
  font-weight: 500;
}

/* Image preview */
.image-preview {
  margin-top: var(--spacing-3);
  max-width: 100%;
  height: 180px;
  background-color: var(--gray-100);
  border-radius: var(--radius-md);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--gray-300);
  transition: border-color var(--transition-fast);
}

.image-preview:hover {
  border-color: var(--primary-light);
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: opacity var(--transition-normal);
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray-100);
  color: var(--gray-500);
  font-size: 2.5rem;
  transition: color var(--transition-fast);
}

.no-image:hover {
  color: var(--primary-light);
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 2rem;
  color: var(--primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  margin-top: var(--spacing-6);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

.btn-secondary {
  background-color: var(--gray-200);
  color: var(--gray-800);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-secondary:hover {
  background-color: var(--gray-300);
  transform: translateY(-2px);
}

/* Tooltip styles */
.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  background-color: var(--gray-800);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--transition-fast), transform var(--transition-fast);
  z-index: 10;
  box-shadow: var(--shadow-md);
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: var(--gray-800) transparent transparent transparent;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-container {
    padding: var(--spacing-4);
  }
  
  /* Ensure admin container takes full width on mobile when sidebar is visible */
  .sidebar:not(.hide):not(.small) ~ .admin-container {
    margin-left: 0;
    width: 100%;
    padding-top: 70px; /* Add space for the header */
  }
  
  .activities-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
  
  .btn-add {
    width: 100%;
    justify-content: center;
  }
  
  .modal-content {
    width: 95%;
    max-height: 85vh;
  }
}

@media (max-width: 480px) {
  .activities-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-primary, .btn-secondary {
    width: 100%;
  }
  
  /* Force sidebar to be hidden on very small screens */
  .sidebar:not(.hide) {
    left: -260px;
  }
  
  /* Ensure admin container takes full width */
  .admin-container {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

/* Animations */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.btn-add:active {
  animation: pulse 0.3s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}