/* Base Styles */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #e67e22;
  --accent-color: #3498db;
  --text-color: #333;
  --light-text: #fff;
  --background-light: #f9f9f9;
  --background-dark: #2c3e50;
  --transition: all 0.3s ease;
  --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --border-radius: 10px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero-section {
  height: 60vh;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
              url('https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--light-text);
}

.hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  animation: fadeInDown 1s ease;
}

.hero-content p {
  font-size: 1.2rem;
  animation: fadeInUp 1s ease;
}

/* Story Section */
.story-section {
  padding: 80px 0;
  background-color: var(--background-light);
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

.story-text h2 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.story-text .subtitle {
  font-size: 1.2rem;
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
}

.story-text .description {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.story-image img {
  width: 100%;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.story-image img:hover {
  transform: scale(1.02);
}

/* Mission Section */
.mission-section {
  padding: 80px 0;
  background-color: var(--background-dark);
  color: var(--light-text);
}

.mission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.mission-card {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.mission-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
}

.mission-card i {
  font-size: 2.5rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.mission-card h3 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

/* Team Section */
.team-section {
  padding: 80px 0;
  background-color: var(--background-light);
  text-align: center;
}

.team-section h2 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.section-subtitle {
  color: var(--secondary-color);
  margin-bottom: 3rem;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.team-member {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.team-member:hover {
  transform: translateY(-10px);
}

.member-image {
  height: 300px;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.team-member:hover .member-image img {
  transform: scale(1.1);
}

.team-member h3 {
  margin: 1rem 0 0.5rem;
  color: var(--primary-color);
}

.team-member .position {
  color: var(--secondary-color);
  font-weight: 500;
  margin-bottom: 1rem;
}

.team-member .bio {
  padding: 0 1.5rem 1.5rem;
  color: #666;
}

/* Values Section */
.values-section {
  padding: 80px 0;
  background-color: white;
  text-align: center;
}

.values-section h2 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.value-card {
  padding: 30px;
  border-radius: var(--border-radius);
  background: var(--background-light);
  transition: var(--transition);
}

.value-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow);
}

.value-card i {
  font-size: 2.5rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.value-card h3 {
  margin-bottom: 1rem;
  color: var(--primary-color);
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: var(--light-text);
  text-align: center;
}

.cta-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta-section p {
  margin-bottom: 2rem;
  font-size: 1.2rem;
}

.cta-button {
  display: inline-block;
  padding: 15px 30px;
  background: var(--secondary-color);
  color: var(--light-text);
  text-decoration: none;
  border-radius: 50px;
  font-weight: bold;
  transition: var(--transition);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.cta-button i {
  margin-right: 10px;
}

/* Animations */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.5rem;
  }

  .story-content {
    grid-template-columns: 1fr;
  }

  .story-text {
    order: 2;
  }

  .story-image {
    order: 1;
  }

  .mission-grid,
  .team-grid,
  .values-grid {
    grid-template-columns: 1fr;
  }

  .team-member {
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-content p {
    font-size: 1rem;
  }

  .story-text h2,
  .team-section h2,
  .values-section h2 {
    font-size: 2rem;
  }

  .cta-section h2 {
    font-size: 2rem;
  }
} 