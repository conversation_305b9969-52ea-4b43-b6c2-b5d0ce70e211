/* Base Styles */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #e67e22;
  --text-color: #333;
  --light-gray: #f5f6fa;
  --white: #ffffff;
  --shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}


/* Contact Section */
.contact-section {
  padding: 80px 0;
  background-color: var(--light-gray);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-top: 40px;
}

/* Contact Form */
.contact-form-container {
  background: var(--white);
  padding: 40px;
  border-radius: 10px;
  box-shadow: var(--shadow);
}

.contact-form-container h2 {
  color: var(--primary-color);
  margin-bottom: 30px;
  font-size: 2rem;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--primary-color);
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(230, 126, 34, 0.1);
}

.submit-btn {
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 12px 30px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: var(--transition);
}

.submit-btn:hover {
  background-color: #d35400;
  transform: translateY(-2px);
}

/* Contact Information */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.info-card {
  background: var(--white);
  padding: 40px;
  border-radius: 10px;
  box-shadow: var(--shadow);
}

.info-card h2 {
  color: var(--primary-color);
  margin-bottom: 30px;
  font-size: 2rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 25px;
}

.info-item i {
  font-size: 1.5rem;
  color: var(--secondary-color);
  background: rgba(230, 126, 34, 0.1);
  padding: 15px;
  border-radius: 50%;
}

.info-item h3 {
  color: var(--primary-color);
  margin-bottom: 5px;
  font-size: 1.1rem;
}

.info-item p {
  color: #666;
  line-height: 1.6;
}

/* Social Links */
.social-links {
  background: var(--white);
  padding: 40px;
  border-radius: 10px;
  box-shadow: var(--shadow);
  text-align: center;
}

.social-links h3 {
  color: var(--primary-color);
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.social-icon {
  width: 45px;
  height: 45px;
  background: var(--light-gray);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.2rem;
  transition: var(--transition);
}

.social-icon:hover {
  background: var(--secondary-color);
  color: var(--white);
  transform: translateY(-3px);
}

/* Map Section */
.map-section {
  padding: 0;
}

.map-container {
  width: 100%;
  height: 450px;
}

.map-container iframe {
  width: 100%;
  height: 100%;
}

/* WhatsApp Float */
.whatsapp-float {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: #25d366;
  color: var(--white);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
  transition: var(--transition);
  z-index: 1000;
}

.whatsapp-float:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 992px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-content h1 {
    font-size: 2.8rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 40vh;
  }
  
  .hero-content h1 {
    font-size: 2.2rem;
  }
  
  .contact-form-container,
  .info-card,
  .social-links {
    padding: 30px;
  }
  
  .info-item {
    flex-direction: column;
    text-align: center;
  }
  
  .info-item i {
    margin: 0 auto 15px;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 1.8rem;
  }
  
  .submit-btn {
    width: 100%;
    justify-content: center;
  }
  
  .whatsapp-float {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
} 