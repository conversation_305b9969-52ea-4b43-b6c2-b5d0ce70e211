@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

:root {
  --color-bg: #f7f8fa;
  --color-sidebar: #232946;
  --color-sidebar-accent: #b85c19;
  --color-primary: #b85c19;
  --color-secondary: #f2c572;
  --color-card: #fff;
  --color-text: #232946;
  --color-muted: #8a8a8a;
  --color-success: #4caf50;
  --color-warning: #ffb300;
  --color-danger: #e74c3c;
  --shadow: 0 4px 24px 0 rgba(35,41,70,0.08);
  --radius: 16px;
  --transition: 0.3s cubic-bezier(.4,2,.6,1);
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --success-color: #2ecc71;
  --danger-color: #e74c3c;
  --light-gray: #f5f6fa;
  --border-color: #dcdde1;
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: 'Poppins', <PERSON><PERSON>, sans-serif;
  background: var(--color-bg);
  color: var(--color-text);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  background-color: var(--light-gray);
  color: var(--primary-color);
}

/* Room thumbnail styles */
.room-thumbnail {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

/* No image placeholder */
.no-image-placeholder {
  width: 80px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 4px;
  color: #6c757d;
  font-size: 0.75rem;
}

.no-image-placeholder i {
  font-size: 1.2rem;
  margin-bottom: 2px;
  color: #adb5bd;
}

/* Icon button styles */
.icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  margin-right: 8px;
}

.icon-btn:last-child {
  margin-right: 0;
}

.icon-btn i {
  font-size: 14px;
}

.edit-btn {
  background-color: #007bff;
  color: white;
}

.edit-btn:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.delete-btn {
  background-color: #dc3545;
  color: white;
}

.delete-btn:hover {
  background-color: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

/* Status badge styles */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  text-align: center;
}

.status-available {
  background-color: #e6f7e6;
  color: #28a745;
  border: 1px solid #28a745;
}

.status-occupied {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.status-maintenance {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Alert styles */
.alert {
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  transition: opacity 0.5s ease;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Form hint */
.form-hint {
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 4px;
}

/* No data message */
.no-data {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

.dashboard-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  background: var(--color-sidebar);
  color: #fff;
  width: 240px;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  padding: 2rem 1rem 1rem 1rem;
  box-shadow: 2px 0 16px 0 rgba(35,41,70,0.07);
}
.sidebar-logo {
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 2.5rem;
  color: var(--color-sidebar-accent);
}
.sidebar-logo i {
  font-size: 1.5rem;
}
.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.sidebar-nav a {
  color: #fff;
  text-decoration: none;
  font-size: 1.08rem;
  padding: 0.7rem 1rem;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  transition: background var(--transition), color var(--transition);
}
.sidebar-nav a.active, .sidebar-nav a:hover {
  background: var(--color-sidebar-accent);
  color: #fff;
}

.main-content {
  flex: 1;
  padding: 2.5rem 2.5rem 2rem 2.5rem;
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2.2rem;
}
.header-title h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--color-primary);
  margin: 0;
}
.header-user {
  display: flex;
  align-items: center;
  gap: 1.1rem;
}
.user-name {
  font-weight: 500;
  color: var(--color-text);
}
.user-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--color-primary);
}
.header-user i.fa-bell {
  font-size: 1.3rem;
  color: var(--color-primary);
  cursor: pointer;
  transition: color var(--transition);
}
.header-user i.fa-bell:hover {
  color: var(--color-secondary);
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
  gap: 2rem;
  margin-bottom: 2.5rem;
}
.stat-card {
  background: var(--color-card);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: 1.2rem;
  padding: 1.5rem 1.2rem;
  transition: box-shadow var(--transition), transform var(--transition);
  cursor: pointer;
}
.stat-card i {
  font-size: 2.1rem;
  color: var(--color-primary);
}
.stat-card h2 {
  font-size: 2rem;
  margin: 0 0 0.2rem 0;
  font-weight: 600;
  color: var(--color-text);
}
.stat-card p {
  margin: 0;
  color: var(--color-muted);
  font-size: 1.08rem;
}
.stat-card:hover {
  box-shadow: 0 10px 36px 0 rgba(184,92,25,0.13);
  transform: translateY(-3px) scale(1.03);
}

.dashboard-table {
  background: var(--color-card);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  margin-top: 1.5rem;
  overflow-x: auto;
}
.dashboard-table h2 {
  font-size: 1.3rem;
  color: var(--color-primary);
  margin-bottom: 1.2rem;
}
.dashboard-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 1.05rem;
}
.dashboard-table th, .dashboard-table td {
  padding: 0.8rem 0.7rem;
  text-align: left;
}
.dashboard-table th {
  background: var(--color-bg);
  color: var(--color-text);
  font-weight: 600;
}
.dashboard-table tr {
  border-bottom: 1px solid #f0f0f0;
}
.dashboard-table tr:last-child {
  border-bottom: none;
}
.status {
  padding: 0.3rem 0.9rem;
  border-radius: 12px;
  font-size: 0.98rem;
  font-weight: 500;
  color: #fff;
  display: inline-block;
}
.status.confirmed {
  background: var(--color-success);
}
.status.pending {
  background: var(--color-warning);
  color: #232946;
}
.status.cancelled {
  background: var(--color-danger);
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .dashboard-container {
    flex-direction: column;
  }
  .sidebar {
    flex-direction: row;
    width: 100%;
    min-width: 0;
    padding: 1rem 0.5rem;
    justify-content: space-between;
    align-items: center;
  }
  .sidebar-logo {
    margin-bottom: 0;
  }
  .sidebar-nav {
    flex-direction: row;
    gap: 0.3rem;
  }
  .sidebar-nav a {
    font-size: 0.98rem;
    padding: 0.5rem 0.7rem;
  }
  .main-content {
    padding: 1.2rem 0.5rem 1rem 0.5rem;
  }
  .dashboard-stats {
    gap: 1rem;
  }
  .dashboard-table {
    padding: 1rem 0.5rem 1rem 0.5rem;
  }
}

@media (max-width: 600px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background: rgba(35,41,70,0.25);
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}
.modal.show {
  display: flex;
}
.modal-content {
  background: var(--color-card);
  margin: auto;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 2.2rem 2rem 1.5rem 2rem;
  max-width: 420px;
  width: 95vw;
  position: relative;
  animation: fadeInUp 0.5s cubic-bezier(.4,2,.6,1);
}
.close-modal {
  position: absolute;
  top: 1.1rem;
  right: 1.3rem;
  font-size: 1.5rem;
  color: var(--color-muted);
  cursor: pointer;
  transition: color 0.2s;
  z-index: 10;
}
.close-modal:hover {
  color: var(--color-danger);
}
#modalTitle {
  margin-top: 0;
  margin-bottom: 1.2rem;
  color: var(--color-primary);
  font-size: 1.3rem;
  font-weight: 600;
}
.form-group {
  margin-bottom: 1.1rem;
}
.form-group label {
  display: block;
  margin-bottom: 0.4rem;
  font-weight: 500;
  color: var(--color-text);
}
.form-group input,
.form-group select {
  width: 100%;
  padding: 0.6rem 0.9rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 1rem;
  background: #fafbfc;
  color: var(--color-text);
  transition: border 0.2s;
}
.form-group input:focus,
.form-group select:focus {
  border: 1.5px solid var(--color-primary);
  outline: none;
}
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.7rem;
  margin-top: 1.2rem;
}
.save-btn {
  background: var(--color-primary);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.3rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.save-btn:hover {
  background: var(--color-secondary);
  color: var(--color-primary);
}
.cancel-btn {
  background: #f0f0f0;
  color: var(--color-text);
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.3rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.cancel-btn:hover {
  background: #e0e0e0;
}
@media (max-width: 600px) {
  .modal-content {
    padding: 1.2rem 0.7rem 1rem 0.7rem;
    max-width: 98vw;
  }
}

.add-room-btn {
  background: var(--color-primary);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.7rem 1.5rem;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.7rem;
  box-shadow: 0 2px 8px 0 rgba(184,92,25,0.10);
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.add-room-btn:hover {
  background: var(--color-secondary);
  color: var(--color-primary);
  box-shadow: 0 4px 16px 0 rgba(184,92,25,0.13);
}

/* Admin Panel Styles */
.admin-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

h1 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  text-align: center;
  font-size: 2.5rem;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: var(--transition);
}

.form-section:hover {
  box-shadow: var(--card-shadow);
}

.form-section h2 {
  color: var(--primary-color);
  margin: 0;
  font-size: 1.8rem;
  font-weight: 500;
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  font-weight: 500;
}

input[type="text"],
textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: var(--transition);
}

input[type="text"]:focus,
textarea:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

/* Team Cards Styles */
.team-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.team-card {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  position: relative;
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.team-card-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
  position: relative;
}

.team-card-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: var(--transition);
}

.team-card:hover .team-card-image::after {
  opacity: 1;
}

.team-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.team-card:hover .team-card-image img {
  transform: scale(1.05);
}

.team-card-content {
  padding: 1.5rem;
}

.team-card-content h3 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 1.4rem;
}

.team-card-content .position {
  color: var(--secondary-color);
  font-weight: 500;
  margin: 0 0 1rem 0;
}

.team-card-content .bio {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
}

.team-card-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: var(--transition);
  z-index: 2;
}

.team-card:hover .team-card-actions {
  opacity: 1;
}

.btn-edit,
.btn-delete {
  background: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  font-size: 1.1rem;
}

.btn-edit {
  color: var(--secondary-color);
}

.btn-delete {
  color: var(--danger-color);
}

.btn-edit:hover {
  background-color: var(--secondary-color);
  color: white;
  transform: scale(1.1);
}

.btn-delete:hover {
  background-color: var(--danger-color);
  color: white;
  transform: scale(1.1);
}

/* Add tooltips to the buttons */
.btn-edit::after,
.btn-delete::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.8rem;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.btn-edit:hover::after,
.btn-delete:hover::after {
  opacity: 1;
  visibility: visible;
  bottom: -35px;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color);
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  transition: var(--transition);
}

.close-modal:hover {
  color: var(--danger-color);
}

.modal-body {
  padding: 1.5rem;
}

/* Button Styles */
.btn-add {
  background-color: var(--success-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  transition: var(--transition);
}

.btn-add:hover {
  background-color: #27ae60;
  transform: translateY(-2px);
}

.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.btn-primary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: var(--light-gray);
  color: var(--primary-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: #e8e8e8;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-container {
    margin: 1rem;
    padding: 1rem;
  }

  .team-cards {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }
}

/* Activities Grid Styles */
.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 1rem;
}

.activity-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.activity-card-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

.activity-card-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: var(--transition);
}

.activity-card:hover .activity-card-image::after {
    opacity: 1;
}

.activity-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.activity-card:hover .activity-card-image img {
    transform: scale(1.05);
}

.activity-card-content {
    padding: 1.5rem;
}

.activity-card-content h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.3rem;
    line-height: 1.4;
}

.activity-card-content .content {
    color: #666;
    font-size: 0.95rem;
    margin: 0;
    line-height: 1.5;
}

.activity-card-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
    z-index: 2;
}

.activity-card:hover .activity-card-actions {
    opacity: 1;
}

/* Image Preview Styles */
.image-preview {
    margin-top: 1rem;
    width: 100%;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

/* Blog Form Styles */
.blog-form {
    max-width: 1200px;
    margin: 0 auto;
}

.blog-form .form-section {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.blog-form .form-group {
    margin-bottom: 1.5rem;
}

.blog-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.blog-form input[type="text"],
.blog-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.blog-form input[type="text"]:focus,
.blog-form textarea:focus {
    border-color: #4CAF50;
    outline: none;
}

/* Content Tools */
.content-tools {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.btn-tool {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-tool:hover {
    background: #e9ecef;
    border-color: #ced4da;
}

.btn-tool i {
    font-size: 1rem;
}

/* Content Sections */
.content-sections {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.content-section {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border-bottom: 1px solid #ddd;
}

.section-type {
    font-weight: 500;
    color: #666;
}

.section-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-move,
.btn-delete {
    padding: 0.25rem 0.5rem;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    transition: color 0.3s ease;
}

.btn-move:hover {
    color: #4CAF50;
}

.btn-delete:hover {
    color: #dc3545;
}

.btn-move:disabled {
    color: #ccc;
    cursor: not-allowed;
}

.section-content {
    padding: 1rem;
}

.section-content textarea {
    width: 100%;
    min-height: 100px;
    resize: vertical;
}

/* Image Input */
.image-input {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.image-preview {
    width: 100%;
    max-width: 300px;
    height: 200px;
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

/* Preview Modal */
.preview-content {
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.blog-preview {
    padding: 2rem;
}

.blog-preview h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.blog-preview h2 {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: #666;
}

.preview-featured-image {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.preview-content-image {
    width: 100%;
    max-height: 500px;
    object-fit: cover;
    border-radius: 8px;
    margin: 2rem 0;
}

.blog-preview p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #444;
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .blog-form .form-section {
        padding: 1.5rem;
    }

    .content-tools {
        flex-direction: column;
    }

    .btn-tool {
        width: 100%;
        justify-content: center;
    }

    .blog-preview h1 {
        font-size: 2rem;
    }

    .blog-preview h2 {
        font-size: 1.25rem;
    }
}

/* Blog Management Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    margin: 0;
    color: #333;
}

.blogs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.blog-card {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.blog-card-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.blog-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
    transform: scale(1.05);
}

.blog-card-content {
    padding: 1.5rem;
}

.blog-card-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    color: #333;
}

.blog-card-content .subtitle {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.blog-card-content .date {
    color: #888;
    font-size: 0.8rem;
    margin: 0;
}

.blog-card-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.blog-card:hover .blog-card-actions {
    opacity: 1;
}

.btn-edit,
.btn-delete {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #666;
}

.btn-edit:hover {
    background-color: #4CAF50;
    color: #fff;
}

.btn-delete:hover {
    background-color: #dc3545;
    color: #fff;
}

/* Delete Modal Styles */
.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn-danger {
    background: #dc3545;
    color: #fff;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .page-header .btn-primary {
        width: 100%;
    }

    .blogs-grid {
        grid-template-columns: 1fr;
    }

    .blog-card-actions {
        opacity: 1;
        background: rgba(255, 255, 255, 0.9);
        padding: 0.5rem;
        border-radius: 4px;
    }
}

/* Car Management Styles */
.cars-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.car-card {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.car-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.car-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

.car-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.car-card:hover .car-image img {
    transform: scale(1.05);
}

.car-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: #4CAF50;
    color: #fff;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    text-transform: capitalize;
}

.car-details {
    padding: 1.5rem;
}

.car-details h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    color: #333;
}

.car-specs {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.car-specs span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.car-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.car-features span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f8f9fa;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #666;
}

.car-price {
    margin-bottom: 1rem;
}

.car-price .price {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.car-price .period {
    color: #666;
    font-size: 0.9rem;
}

.car-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
}

/* Form Styles */
select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    background-color: #fff;
    cursor: pointer;
}

select:focus {
    border-color: #4CAF50;
    outline: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cars-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .car-specs,
    .car-features {
        justify-content: center;
    }
}

/* Contact Form Styles */
.contact-form {
    max-width: 800px;
    margin: 0 auto;
}

.contact-form .form-section {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.contact-form h2 {
    margin: 0 0 1.5rem 0;
    color: #333;
    font-size: 1.5rem;
}

.coordinates-input {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.map-preview {
    width: 100%;
    height: 300px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 1rem;
}

.map-preview iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Preview Modal Styles */
.preview-content {
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.contact-preview {
    padding: 2rem;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.info-item i {
    font-size: 1.5rem;
    color: #4CAF50;
    width: 2rem;
    text-align: center;
}

.info-item span {
    color: #333;
    font-size: 1.1rem;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.social-links a {
    color: #666;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #4CAF50;
}

.map-container {
    width: 100%;
    height: 450px;
    border-radius: 8px;
    overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-form .form-section {
        padding: 1.5rem;
    }

    .coordinates-input {
        grid-template-columns: 1fr;
    }

    .contact-info {
        grid-template-columns: 1fr;
    }

    .map-preview {
        height: 250px;
    }
}

/* Events Management Styles */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.event-card {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.event-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.event-content {
    padding: 1.5rem;
}

.event-content h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.25rem;
}

.event-date {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.event-description {
    color: #444;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.event-actions {
    display: flex;
    gap: 1rem;
}

.btn-edit, .btn-delete {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: background-color 0.3s ease;
}

.btn-edit {
    background-color: #4CAF50;
    color: white;
}

.btn-delete {
    background-color: #f44336;
    color: white;
}

.btn-edit:hover {
    background-color: #45a049;
}

.btn-delete:hover {
    background-color: #da190b;
}

/* Event Form Styles */
.image-preview {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .events-grid {
        grid-template-columns: 1fr;
    }

    .event-actions {
        flex-direction: column;
    }

    .btn-edit, .btn-delete {
        width: 100%;
    }
}

/* Hero Management Styles */
.hero-slides-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.hero-slide-card {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-slide-card h2 {
    margin: 0 0 1.5rem 0;
    color: #333;
    font-size: 1.25rem;
}

/* Hero Preview Styles */
.hero-preview {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
    border-radius: 8px;
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.hero-slide.active {
    opacity: 1;
}

.hero-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-slide .hero-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 2rem;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
}

.hero-slide .hero-content h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
}

.hero-slide .hero-content p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

.hero-slider-controls {
    position: absolute;
    bottom: 1rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding: 0 1rem;
}

.slider-control {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.slider-control:hover {
    background: rgba(255, 255, 255, 0.3);
}

.slider-dots {
    display: flex;
    gap: 0.5rem;
}

.slider-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.slider-dot.active {
    background: white;
}

/* Preview Modal Styles */
.preview-content {
    max-width: 1000px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-slides-container {
        grid-template-columns: 1fr;
    }

    .hero-preview {
        height: 400px;
    }

    .hero-slide .hero-content h1 {
        font-size: 1.5rem;
    }

    .hero-slide .hero-content p {
        font-size: 1rem;
    }
}

/* About Section Management Styles */
.about-edit-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.about-edit-container .form-section {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.about-edit-container h2 {
    margin: 0 0 1.5rem 0;
    color: #333;
    font-size: 1.5rem;
}

.about-edit-container textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
}

.about-edit-container textarea:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* About Preview Styles */
.about-preview {
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.about-content h2 {
    color: #333;
    font-size: 2rem;
    margin: 0 0 1.5rem 0;
}

.about-content p {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
}

.about-content p:last-child {
    margin-bottom: 0;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Preview Modal Styles */
.preview-content {
    max-width: 1000px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .about-edit-container {
        padding: 1rem;
    }

    .about-edit-container .form-section {
        padding: 1.5rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
    }

    .about-content h2 {
        font-size: 1.75rem;
    }

    .about-content p {
        font-size: 1rem;
    }
}

/* Home Content Management Styles */
.section-header-edit {
    max-width: 800px;
    margin: 0 auto 2rem;
}

.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 0;
}

.service-card {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.service-card.reverse {
    direction: rtl;
}

.service-card.reverse .service-content {
    direction: ltr;
}

.service-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-content {
    padding: 2rem;
}

.service-content h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.5rem;
}

.service-content p {
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
}

.service-actions {
    display: flex;
    gap: 1rem;
}

.btn-edit, .btn-delete {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: background-color 0.3s ease;
}

.btn-edit {
    background-color: #4CAF50;
    color: white;
}

.btn-delete {
    background-color: #f44336;
    color: white;
}

.btn-edit:hover {
    background-color: #45a049;
}

.btn-delete:hover {
    background-color: #da190b;
}

/* Service Form Styles */
.image-preview {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .service-card {
        grid-template-columns: 1fr;
    }

    .service-card.reverse {
        direction: ltr;
    }

    .service-image {
        height: 200px;
    }

    .service-content {
        padding: 1.5rem;
    }

    .service-actions {
        flex-direction: column;
    }

    .btn-edit, .btn-delete {
        width: 100%;
    }
}

/* Admin Layout Styles */
.admin-container {
    display: flex;
    min-height: 100vh;
    background-color: #f5f5f5;
}

.admin-sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: white;
    padding: 1rem;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.admin-content {
    flex: 1;
    margin-left: 250px;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto 0 250px;
}

/* Sidebar Styles */
.sidebar-header {
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: white;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 0.5rem;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sidebar-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar-menu a.active {
    background-color: #3498db;
    color: white;
}

.sidebar-menu i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

/* Content Header Styles */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #ddd;
}

.content-header h1 {
    margin: 0;
    font-size: 1.8rem;
    color: #2c3e50;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn i {
    margin-right: 0.5rem;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-success {
    background-color: #2ecc71;
    color: white;
}

.btn-success:hover {
    background-color: #27ae60;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #3498db;
    outline: none;
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.card-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #2c3e50;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: white;
    margin: 2rem auto;
    padding: 2rem;
    border-radius: 8px;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: #2c3e50;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0;
}

.close-modal:hover {
    color: #333;
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        width: 0;
        padding: 0;
        transform: translateX(-100%);
        transition: all 0.3s ease;
    }

    .admin-sidebar.active {
        width: 250px;
        padding: 1rem;
        transform: translateX(0);
    }

    .admin-content {
        margin-left: 0;
        padding: 1rem;
    }

    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .modal-content {
        margin: 1rem;
        padding: 1rem;
    }

    .content-container {
        padding: 0 1rem;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}