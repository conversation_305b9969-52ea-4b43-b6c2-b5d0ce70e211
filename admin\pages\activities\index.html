<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activities Management - Virunga Homestay Admin</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/modals.css">
    <link rel="stylesheet" href="../../assets/css/tooltips.css">
    <link rel="stylesheet" href="../../assets/css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="dashboard-page">
    <!-- Connectivity Indicator -->
    <div id="connectivityStatus" class="connectivity-indicator online">
        <i class="fas fa-wifi"></i>
        <span class="status-text">Online</span>
    </div>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-mountain"></i>
                    <span>Virunga Admin</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <div class="sidebar-menu">
                <ul class="menu-list">
                    <li class="menu-item">
                        <a href="../../dashboard.html" class="menu-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="menu-item active">
                        <a href="index.html" class="menu-link">
                            <i class="fas fa-hiking"></i>
                            <span>Activities</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../blogs/index.html" class="menu-link">
                            <i class="fas fa-blog"></i>
                            <span>Blog Posts</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../cars/index.html" class="menu-link">
                            <i class="fas fa-car"></i>
                            <span>Car Rental</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../events/index.html" class="menu-link">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Events</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../rooms/index.html" class="menu-link">
                            <i class="fas fa-bed"></i>
                            <span>Rooms</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../services/index.html" class="menu-link">
                            <i class="fas fa-concierge-bell"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../reviews/index.html" class="menu-link">
                            <i class="fas fa-star"></i>
                            <span>Reviews</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../hero-images/index.html" class="menu-link">
                            <i class="fas fa-images"></i>
                            <span>Hero Images</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../homepage/about.html" class="menu-link">
                            <i class="fas fa-info-circle"></i>
                            <span>Homepage About</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="../messages/index.html" class="menu-link">
                            <i class="fas fa-envelope"></i>
                            <span>Messages</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-footer">
                <button class="btn btn-secondary btn-full" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="main-header">
                <div class="header-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1>Activities Management</h1>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" onclick="showModal('createActivityModal')">
                        <i class="fas fa-plus"></i>
                        Add Activity
                    </button>
                    <div class="user-info">
                        <i class="fas fa-user-circle"></i>
                        <span>Admin User</span>
                    </div>
                </div>
            </header>

            <!-- Activities Content -->
            <div class="dashboard-content">
                <!-- Filters and Search -->
                <div class="filters-section" style="background: white; padding: 1.5rem; border-radius: 0.75rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-bottom: 2rem;">
                    <div style="display: grid; grid-template-columns: 1fr auto auto; gap: 1rem; align-items: center;">
                        <div class="search-box">
                            <div class="input-group">
                                <i class="fas fa-search"></i>
                                <input type="text" id="searchActivities" class="form-input" placeholder="Search activities...">
                            </div>
                        </div>
                        <select id="statusFilter" class="form-input" style="width: auto;">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="draft">Draft</option>
                        </select>
                        <button class="btn btn-secondary" onclick="refreshActivities()">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Activities Table -->
                <div class="data-table-container">
                    <table class="data-table" id="activitiesTable">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th data-sortable>Title</th>
                                <th data-sortable>Duration</th>
                                <th data-sortable>Price</th>
                                <th data-sortable>Status</th>
                                <th data-sortable>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="activitiesTableBody">
                            <!-- Activities will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div id="paginationContainer" class="pagination-container" style="margin-top: 2rem; text-align: center;">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Create Activity Modal -->
    <div id="createActivityModal" class="modal">
        <div class="modal-content form-modal">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> Add New Activity</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createActivityForm" data-autosave>
                    <div class="form-grid-2">
                        <div class="form-group">
                            <label for="activityTitle" class="form-label">
                                Title *
                                <i class="fas fa-info-circle tooltip-trigger" data-tooltip="Enter the activity title"></i>
                            </label>
                            <input type="text" id="activityTitle" name="title" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="activityDuration" class="form-label">
                                Duration
                                <i class="fas fa-info-circle tooltip-trigger" data-tooltip="Activity duration (e.g., 2 hours, Full day)"></i>
                            </label>
                            <input type="text" id="activityDuration" name="duration" class="form-input">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="activityDescription" class="form-label">
                            Description *
                            <i class="fas fa-info-circle tooltip-trigger" data-tooltip="Detailed description of the activity"></i>
                        </label>
                        <textarea id="activityDescription" name="description" class="form-input" rows="4" required></textarea>
                    </div>

                    <div class="form-grid-2">
                        <div class="form-group">
                            <label for="activityPrice" class="form-label">
                                Price (USD)
                                <i class="fas fa-info-circle tooltip-trigger" data-tooltip="Price in US Dollars"></i>
                            </label>
                            <input type="number" id="activityPrice" name="price" class="form-input" step="0.01" min="0">
                        </div>
                        <div class="form-group">
                            <label for="activityStatus" class="form-label">
                                Status *
                                <i class="fas fa-info-circle tooltip-trigger" data-tooltip="Activity status"></i>
                            </label>
                            <select id="activityStatus" name="status" class="form-input" required>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="draft">Draft</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="activityImages" class="form-label">
                            Images
                            <i class="fas fa-info-circle tooltip-trigger" data-tooltip="Upload activity images (max 5MB each)"></i>
                        </label>
                        <div class="image-upload-area" onclick="document.getElementById('activityImages').click()">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <h4>Click to upload images</h4>
                            <p>Or drag and drop files here</p>
                        </div>
                        <input type="file" id="activityImages" name="images[]" multiple accept="image/*" style="display: none;">
                        <div id="imagePreviewContainer" class="image-preview-container"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="submit" form="createActivityForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Save Activity
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Activity Modal -->
    <div id="editActivityModal" class="modal">
        <div class="modal-content form-modal">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> Edit Activity</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editActivityForm">
                    <input type="hidden" id="editActivityId" name="id">
                    <!-- Same form fields as create modal -->
                    <div class="form-grid-2">
                        <div class="form-group">
                            <label for="editActivityTitle" class="form-label">Title *</label>
                            <input type="text" id="editActivityTitle" name="title" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="editActivityDuration" class="form-label">Duration</label>
                            <input type="text" id="editActivityDuration" name="duration" class="form-input">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editActivityDescription" class="form-label">Description *</label>
                        <textarea id="editActivityDescription" name="description" class="form-input" rows="4" required></textarea>
                    </div>

                    <div class="form-grid-2">
                        <div class="form-group">
                            <label for="editActivityPrice" class="form-label">Price (USD)</label>
                            <input type="number" id="editActivityPrice" name="price" class="form-input" step="0.01" min="0">
                        </div>
                        <div class="form-group">
                            <label for="editActivityStatus" class="form-label">Status *</label>
                            <select id="editActivityStatus" name="status" class="form-input" required>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="draft">Draft</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="submit" form="editActivityForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Update Activity
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <script src="../../assets/js/utils.js"></script>
    <script src="../../assets/js/connectivity.js"></script>
    <script src="../../assets/js/tooltips.js"></script>
    <script src="../../assets/js/modals.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script src="activities.js"></script>
</body>
</html>
