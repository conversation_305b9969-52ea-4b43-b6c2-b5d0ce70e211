/* Modern Blog Admin CSS */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Modern color palette */
  --primary: #4361ee;
  --primary-light: #4895ef;
  --primary-dark: #3a0ca3;
  --secondary: #f72585;
  --success: #4cc9f0;
  --warning: #f8961e;
  --danger: #f94144;
  --light: #f8f9fa;
  --dark: #212529;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  
  /* Text colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-muted: var(--gray-600);
  --text-light: var(--gray-100);
  
  /* Background colors */
  --bg-light: #f5f7fa;
  --bg-white: #ffffff;
  --bg-primary: var(--primary);
  --bg-secondary: var(--secondary);
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(67, 97, 238, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
}

/* Base styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-light);
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

.admin-container {
  padding: var(--spacing-6);
  padding-top: calc(var(--spacing-6) + 60px); /* Add space for header height */
  max-width: 1400px;
  margin: 0 auto;
  margin-left: 260px; /* Match sidebar width */
  animation: fadeIn 0.5s ease-out;
  width: calc(100% - 260px); /* Adjust width to account for sidebar */
  transition: margin-left 0.3s ease, width 0.3s ease; /* Smooth transition when sidebar changes */
  position: relative;
  min-height: 100vh;
}

/* Adjust container when sidebar is in small mode */
.sidebar.small ~ .admin-container {
  margin-left: 70px; /* Match small sidebar width */
  width: calc(100% - 70px);
}

/* Adjust container when sidebar is hidden */
.sidebar.hide ~ .admin-container {
  margin-left: 0;
  width: 100%;
}

/* Typography */
h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-6);
  position: relative;
  display: inline-block;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  border-radius: var(--radius-full);
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-4);
}

/* Alerts */
.alert {
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  border-radius: var(--radius-md);
  font-weight: 500;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-sm);
  animation: slideInDown 0.3s ease-out forwards;
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
}

.alert-success {
  background-color: rgba(76, 201, 240, 0.1);
  color: var(--success);
  border: 1px solid rgba(76, 201, 240, 0.2);
}

.alert-success::before {
  background-color: var(--success);
}

.alert-error {
  background-color: rgba(249, 65, 68, 0.1);
  color: var(--danger);
  border: 1px solid rgba(249, 65, 68, 0.2);
}

.alert-error::before {
  background-color: var(--danger);
}

/* Form styles */
.blog-form {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: var(--transition-normal);
}

.form-section {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
}

.form-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.form-group {
  margin-bottom: var(--spacing-5);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  color: var(--text-secondary);
}

.form-group input[type="text"],
.form-group textarea {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: var(--transition-fast);
  background-color: var(--bg-white);
  color: var(--text-primary);
}

.form-group input[type="text"]:focus,
.form-group textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
  outline: none;
}

.form-group input[type="file"] {
  display: block;
  width: 100%;
  padding: var(--spacing-3) 0;
}

/* Custom file input */
.file-input-container {
  position: relative;
  margin-bottom: var(--spacing-4);
}

.file-input-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--gray-100);
  border: 1px dashed var(--gray-400);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--text-secondary);
}

.file-input-label:hover {
  background-color: var(--gray-200);
  border-color: var(--primary-light);
}

.file-input-label i {
  margin-right: var(--spacing-2);
  font-size: 1.2rem;
  color: var(--primary);
}

.file-input {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* Image preview */
.image-preview {
  width: 100%;
  height: 200px;
  margin-top: var(--spacing-4);
  background-color: var(--gray-100);
  border-radius: var(--radius-md);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: var(--transition-normal);
}

.image-preview::before {
  content: 'No image selected';
  position: absolute;
  color: var(--text-muted);
  font-size: 0.9rem;
  opacity: 0.7;
}

.image-preview.has-image::before {
  display: none;
}

/* Content sections */
.content-sections {
  margin-bottom: var(--spacing-6);
}

.content-section {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-4);
  overflow: hidden;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.content-section:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--gray-100);
  border-bottom: 1px solid var(--gray-200);
}

.section-type {
  font-weight: 500;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.section-type i {
  margin-right: var(--spacing-2);
  color: var(--primary);
}

.section-actions {
  display: flex;
  gap: var(--spacing-2);
}

.section-content {
  padding: var(--spacing-4);
}

.section-content textarea {
  width: 100%;
  min-height: 120px;
  padding: var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  resize: vertical;
  transition: var(--transition-fast);
}

.section-content textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
  outline: none;
}

.image-input {
  display: flex;
  flex-direction: column;
}

/* Content tools */
.content-tools {
  display: flex;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.btn-tool {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--bg-white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.btn-tool:hover {
  background-color: var(--gray-100);
  border-color: var(--primary-light);
  color: var(--primary);
}

.btn-tool i {
  font-size: 1.1rem;
  color: var(--primary);
}

/* Action buttons */
.btn-move,
.btn-delete {
  background: none;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--text-muted);
}

.btn-move:hover {
  background-color: var(--gray-200);
  color: var(--primary);
}

.btn-move:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-delete:hover {
  background-color: rgba(249, 65, 68, 0.1);
  color: var(--danger);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding: var(--spacing-6);
  background-color: var(--gray-100);
  border-top: 1px solid var(--gray-200);
}

.btn-primary,
.btn-secondary {
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-white);
  color: var(--text-secondary);
  border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  display: flex;
  animation: fadeIn 0.3s forwards;
}

.modal-content {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  transform: translateY(20px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal.show .modal-content {
  transform: translateY(0);
  opacity: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h2 {
  margin: 0;
  color: var(--text-primary);
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition-fast);
}

.close-modal:hover {
  color: var(--danger);
}

.modal-body {
  padding: var(--spacing-6);
}

.blog-preview {
  font-family: 'Inter', sans-serif;
}

.blog-preview h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-3);
}

.blog-preview h2 {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-6);
  font-weight: 400;
}

.preview-featured-image {
  width: 100%;
  max-height: 400px;
  object-fit: cover;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-6);
}

.preview-content-image {
  max-width: 100%;
  border-radius: var(--radius-md);
  margin: var(--spacing-4) 0;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-container {
    padding: var(--spacing-4);
    margin-left: 0;
    width: 100%;
  }
  
  .form-section {
    padding: var(--spacing-4);
  }
  
  .form-actions {
    padding: var(--spacing-4);
    flex-direction: column;
  }
  
  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
  
  .content-tools {
    flex-wrap: wrap;
  }
  
  .btn-tool {
    flex: 1;
    justify-content: center;
  }
  
  .modal-content {
    width: 95%;
    max-height: 85vh;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.5rem;
  }
  
  h2 {
    font-size: 1.2rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
    margin-top: var(--spacing-2);
  }
}