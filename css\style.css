/* CSS Variables */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #f39c12;
    --accent-color: #e74c3c;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --dark-gray: #34495e;
    --border-color: #ecf0f1;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --container-width: 1200px;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Glassmorphism Header Styles */
.glassmorphism-header {
    background: rgba(44, 90, 160, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.glass-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.header-content {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
    position: relative;
    z-index: 2;
}

/* Logo Styles */
.header-logo {
    position: relative;
}

.logo-link {
    text-decoration: none;
    color: var(--white);
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.1rem 0.8rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.logo-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.logo-icon {
    font-size: 1.5rem;
    color: var(--secondary-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-text h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(45deg, var(--white), #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-subtitle {
    font-size: 0.7rem;
    color: var(--secondary-color);
    font-weight: 300;
    margin-top: -2px;
}

/* Navigation Styles */
.nav-menu {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 0.8rem;
    align-items: center;
}

.nav-link {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    min-width: 100px;
    justify-content: center;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

.nav-icon {
    font-size: 1rem;
    color: var(--secondary-color);
    transition: var(--transition);
}

.nav-text {
    font-size: 0.85rem;
    font-weight: 500;
}

.nav-link:hover .nav-icon {
    transform: scale(1.2);
    color: var(--white);
}

/* Book Now Button */
.glass-button {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #e67e22 100%) !important;
    color: var(--white) !important;
    padding: 0.5rem 1.2rem !important;
    border-radius: 20px !important;
    font-weight: 600 !important;
    transition: var(--transition) !important;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3) !important;
}

.glass-button:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4) !important;
}

.button-icon {
    font-size: 1rem;
    margin-right: 0.4rem;
}

.button-text {
    font-weight: 600;
    font-size: 0.85rem;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.4rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.mobile-menu-toggle span {
    width: 22px;
    height: 2px;
    background: var(--white);
    margin: 2px 0;
    transition: var(--transition);
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Footer Styles */
.footer {
    background: linear-gradient(135deg, var(--dark-gray) 0%, #2c3e50 100%);
    color: var(--white);
    padding: 0;
    margin-top: 4rem;
}

.footer-content {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
}

/* Main Footer Sections */
.footer-main {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    padding: 4rem 0 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Company Info Section */
.company-info {
    grid-column: 1;
}

.footer-logo h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--white), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-tagline {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.footer-description {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Social Links */
.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* Footer Sections */
.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--white);
    position: relative;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--secondary-color);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: var(--transition);
    display: inline-block;
    position: relative;
}

.footer-links a:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.footer-links a::before {
    content: '→';
    position: absolute;
    left: -15px;
    opacity: 0;
    transition: var(--transition);
}

.footer-links a:hover::before {
    opacity: 1;
    left: -20px;
}

/* Contact Info */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #bdc3c7;
    transition: var(--transition);
}

.contact-item:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.contact-item svg {
    color: var(--secondary-color);
    flex-shrink: 0;
}

/* Newsletter Section */
.footer-newsletter {
    padding: 3rem 0;
    background: rgba(255, 255, 255, 0.05);
    margin: 2rem 0;
    border-radius: var(--border-radius);
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-content h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--white);
}

.newsletter-content p {
    color: #bdc3c7;
    margin-bottom: 2rem;
}

.newsletter-form .form-group {
    display: flex;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: 1rem;
    backdrop-filter: blur(10px);
}

.newsletter-form input::placeholder {
    color: #bdc3c7;
}

.newsletter-form input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px var(--secondary-color);
}

.newsletter-form button {
    padding: 1rem 2rem;
    background: var(--secondary-color);
    color: var(--white);
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.newsletter-form button:hover {
    background: #e67e22;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

/* Bottom Footer */
.footer-bottom {
    padding: 2rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: #bdc3c7;
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    gap: 2rem;
}

.footer-bottom-links a {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .footer-main {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .company-info {
        grid-column: 1 / -1;
    }
}

@media (max-width: 768px) {
    /* Header Mobile Styles */
    .nav-menu {
        position: fixed;
        top: 60px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 60px);
        background: linear-gradient(135deg, rgba(44, 90, 160, 0.95) 0%, rgba(26, 74, 138, 0.95) 100%);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
        box-shadow: var(--shadow-hover);
        border-top: 1px solid var(--glass-border);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
        padding: 0 2rem;
    }
    
    .nav-link {
        width: 100%;
        text-align: center;
        padding: 1rem;
        border-radius: var(--border-radius);
        justify-content: center;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* Footer Mobile Styles */
    .footer-main {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .contact-item {
        justify-content: center;
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
        gap: 1rem;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-bottom-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 1rem;
    }
    
    .logo-text h1 {
        font-size: 1.5rem;
    }
    
    .nav-link {
        min-width: auto;
        padding: 0.75rem 1rem;
    }
    
    .footer-content {
        padding: 0 1rem;
    }
    
    .footer-main {
        padding: 2rem 0 1rem;
    }
    
    .footer-newsletter {
        padding: 2rem 1rem;
        margin: 1rem 0;
    }
    
    .newsletter-form input,
    .newsletter-form button {
        padding: 0.8rem 1.2rem;
    }
    
    .footer-bottom-links {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Animation for mobile menu */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}
