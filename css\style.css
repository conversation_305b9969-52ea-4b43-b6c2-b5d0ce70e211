/* CSS Variables */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #f39c12;
    --accent-color: #e74c3c;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --dark-gray: #34495e;
    --border-color: #ecf0f1;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --container-width: 1200px;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Glassmorphism Header Styles */
.glassmorphism-header {
    background: rgba(44, 90, 160, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.glass-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.header-content {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
    position: relative;
    z-index: 2;
}

/* Logo Styles */
.header-logo {
    position: relative;
}

.logo-link {
    text-decoration: none;
    color: var(--white);
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.1rem 0.8rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.logo-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.logo-icon {
    font-size: 1.5rem;
    color: var(--secondary-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-text h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(45deg, var(--white), #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-subtitle {
    font-size: 0.7rem;
    color: var(--secondary-color);
    font-weight: 300;
    margin-top: -2px;
}

/* Navigation Styles */
.nav-menu {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 0.8rem;
    align-items: center;
}

.nav-link {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    min-width: 100px;
    justify-content: center;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

.nav-icon {
    font-size: 1rem;
    color: var(--secondary-color);
    transition: var(--transition);
}

.nav-text {
    font-size: 0.85rem;
    font-weight: 500;
}

.nav-link:hover .nav-icon {
    transform: scale(1.2);
    color: var(--white);
}

/* Book Now Button */
.glass-button {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #e67e22 100%) !important;
    color: var(--white) !important;
    padding: 0.5rem 1.2rem !important;
    border-radius: 20px !important;
    font-weight: 600 !important;
    transition: var(--transition) !important;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3) !important;
}

.glass-button:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4) !important;
}

.button-icon {
    font-size: 1rem;
    margin-right: 0.4rem;
}

.button-text {
    font-weight: 600;
    font-size: 0.85rem;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.4rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.mobile-menu-toggle span {
    width: 22px;
    height: 2px;
    background: var(--white);
    margin: 2px 0;
    transition: var(--transition);
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Footer Styles */
.footer {
    background: linear-gradient(135deg, var(--dark-gray) 0%, #2c3e50 100%);
    color: var(--white);
    padding: 0;
    margin-top: 4rem;
}

.footer-content {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
}

/* Main Footer Sections */
.footer-main {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    padding: 4rem 0 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Company Info Section */
.company-info {
    grid-column: 1;
}

.footer-logo h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--white), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-tagline {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.footer-description {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Social Links */
.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* Footer Sections */
.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--white);
    position: relative;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--secondary-color);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: var(--transition);
    display: inline-block;
    position: relative;
}

.footer-links a:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.footer-links a::before {
    content: '→';
    position: absolute;
    left: -15px;
    opacity: 0;
    transition: var(--transition);
}

.footer-links a:hover::before {
    opacity: 1;
    left: -20px;
}

/* Contact Info */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #bdc3c7;
    transition: var(--transition);
}

.contact-item:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.contact-item svg {
    color: var(--secondary-color);
    flex-shrink: 0;
}

/* Newsletter Section */
.footer-newsletter {
    padding: 3rem 0;
    background: rgba(255, 255, 255, 0.05);
    margin: 2rem 0;
    border-radius: var(--border-radius);
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-content h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--white);
}

.newsletter-content p {
    color: #bdc3c7;
    margin-bottom: 2rem;
}

.newsletter-form .form-group {
    display: flex;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: 1rem;
    backdrop-filter: blur(10px);
}

.newsletter-form input::placeholder {
    color: #bdc3c7;
}

.newsletter-form input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px var(--secondary-color);
}

.newsletter-form button {
    padding: 1rem 2rem;
    background: var(--secondary-color);
    color: var(--white);
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.newsletter-form button:hover {
    background: #e67e22;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

/* Bottom Footer */
.footer-bottom {
    padding: 2rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: #bdc3c7;
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    gap: 2rem;
}

.footer-bottom-links a {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .footer-main {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .company-info {
        grid-column: 1 / -1;
    }
}

@media (max-width: 768px) {
    /* Header Mobile Styles */
    .nav-menu {
        position: fixed;
        top: 60px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 60px);
        background: linear-gradient(135deg, rgba(44, 90, 160, 0.95) 0%, rgba(26, 74, 138, 0.95) 100%);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
        box-shadow: var(--shadow-hover);
        border-top: 1px solid var(--glass-border);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
        padding: 0 2rem;
    }
    
    .nav-link {
        width: 100%;
        text-align: center;
        padding: 1rem;
        border-radius: var(--border-radius);
        justify-content: center;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* Footer Mobile Styles */
    .footer-main {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .contact-item {
        justify-content: center;
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
        gap: 1rem;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-bottom-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 1rem;
    }
    
    .logo-text h1 {
        font-size: 1.5rem;
    }
    
    .nav-link {
        min-width: auto;
        padding: 0.75rem 1rem;
    }
    
    .footer-content {
        padding: 0 1rem;
    }
    
    .footer-main {
        padding: 2rem 0 1rem;
    }
    
    .footer-newsletter {
        padding: 2rem 1rem;
        margin: 1rem 0;
    }
    
    .newsletter-form input,
    .newsletter-form button {
        padding: 0.8rem 1.2rem;
    }
    
    .footer-bottom-links {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Animation for mobile menu */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

/* Homepage Sections Styles */

/* Stats Section */
.stats-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
}

.stats-container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--white);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* About Section */
.about-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.about-grid {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-content h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.about-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* Services Section */
.services-section {
    padding: 100px 0;
    background: var(--white);
}

.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.services-grid {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

.service-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.service-item.reverse {
    direction: rtl;
}

.service-item.reverse > * {
    direction: ltr;
}

.service-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.service-content h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-light);
}

/* Rooms Section */
.rooms-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.rooms-grid {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.room-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.room-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.room-image {
    height: 250px;
    overflow: hidden;
}

.room-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.room-card:hover .room-image img {
    transform: scale(1.05);
}

.room-content {
    padding: 1.5rem;
}

.room-title {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.room-description {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.room-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--secondary-color);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
}

.view-all-rooms {
    text-align: center;
    margin-top: 2rem;
}

.no-rooms-message {
    text-align: center;
    padding: 3rem;
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Experiences Section */
.experiences-section {
    padding: 100px 0;
    background: var(--white);
}

.experiences-grid {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.experience-card-link {
    text-decoration: none;
    color: inherit;
}

.experience-card {
    position: relative;
    height: 300px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.experience-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.experience-card.hidden {
    display: none;
}

.card-image {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.experience-card:hover .card-image img {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
}

.card-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    color: var(--white);
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
}

/* Attractions Section */
.attractions-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: var(--white);
}

.attractions-grid {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.attraction-card {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.attraction-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.attraction-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.attraction-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--white);
}

.attraction-card p {
    line-height: 1.6;
    opacity: 0.9;
}

/* Testimonials Section */
.testimonials-section {
    padding: 100px 0;
    background: var(--light-gray);
}

.testimonials-container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 2rem;
}

.testimonials-header {
    text-align: center;
    margin-bottom: 3rem;
}

.testimonials-title {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.testimonials-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.reviews-scroller {
    margin-bottom: 2rem;
    min-height: 200px;
}

.review-us-btn {
    display: block;
    margin: 0 auto;
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
}

.review-us-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--white);
    margin: 5% auto;
    padding: 2rem;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    position: relative;
    box-shadow: var(--shadow-hover);
}

.close-btn {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 2rem;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
}

.close-btn:hover {
    color: var(--text-dark);
}

.modal-form-group {
    margin-bottom: 1.5rem;
}

.modal-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 500;
}

.modal-form-group input,
.modal-form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.modal-form-group input:focus,
.modal-form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.modal-form-group textarea {
    height: 120px;
    resize: vertical;
}

.rating-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.star-label {
    font-size: 2rem;
    color: #ddd;
    cursor: pointer;
    transition: var(--transition);
}

.star-label:hover,
.star-label.active {
    color: #ffd700;
}

.modal form button {
    width: 100%;
    padding: 1rem;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
}

.modal form button:hover {
    background: var(--secondary-color);
}

/* Floating WhatsApp Button */
.floating-whatsapp {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
    transition: var(--transition);
    z-index: 999;
}

.floating-whatsapp:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.4);
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: 2rem;
}

.load-more-btn {
    background: #0a7b83;
    color: var(--white);
    border: none;
    border-radius: 24px;
    padding: 12px 32px;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.load-more-btn:hover {
    background: #0a6b73;
    transform: translateY(-2px);
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Additional Responsive Design for Homepage Sections */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }

    .about-content h2 {
        font-size: 2rem;
    }

    .service-item {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-item.reverse {
        direction: ltr;
    }

    .section-title {
        font-size: 2rem;
    }

    .rooms-grid {
        grid-template-columns: 1fr;
        padding: 0 1rem;
    }

    .experiences-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        padding: 0 1rem;
    }

    .attractions-grid {
        grid-template-columns: 1fr;
        padding: 0 1rem;
    }

    .testimonials-container {
        padding: 0 1rem;
    }

    .testimonials-title {
        font-size: 2rem;
    }

    .floating-whatsapp {
        bottom: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
    }

    .modal-content {
        margin: 10% auto;
        padding: 1.5rem;
        width: 95%;
    }
}

@media (max-width: 480px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .about-content h2 {
        font-size: 1.8rem;
    }

    .testimonials-title {
        font-size: 1.8rem;
    }

    .room-buttons {
        flex-direction: column;
    }

    .experiences-grid {
        grid-template-columns: 1fr;
    }
}
