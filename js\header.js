// Modern Header functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileNavOverlay = document.getElementById('mobileNavOverlay');
    const mobileNavClose = document.getElementById('mobileNavClose');

    // Open mobile menu
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenuBtn.classList.toggle('active');
            mobileNavOverlay.classList.toggle('active');
            document.body.style.overflow = mobileNavOverlay.classList.contains('active') ? 'hidden' : 'auto';
        });
    }

    // Close mobile menu
    if (mobileNavClose) {
        mobileNavClose.addEventListener('click', function() {
            closeMobileMenu();
        });
    }

    // Close mobile menu when clicking overlay
    if (mobileNavOverlay) {
        mobileNavOverlay.addEventListener('click', function(e) {
            if (e.target === mobileNavOverlay) {
                closeMobileMenu();
            }
        });
    }

    // Close mobile menu when clicking on mobile nav links
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            closeMobileMenu();
        });
    });

    function closeMobileMenu() {
        if (mobileMenuBtn) mobileMenuBtn.classList.remove('active');
        if (mobileNavOverlay) mobileNavOverlay.classList.remove('active');
        document.body.style.overflow = 'auto';
    }

    // Header scroll effect for modern header
    const modernHeader = document.querySelector('.modern-header');
    if (modernHeader) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                modernHeader.style.background = 'rgba(255, 255, 255, 0.98)';
                modernHeader.style.boxShadow = '0 4px 25px rgba(0, 0, 0, 0.15)';
            } else {
                modernHeader.style.background = 'rgba(255, 255, 255, 0.95)';
                modernHeader.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            }
        });
    }

    // Active navigation link highlighting
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-item .nav-link');
    const mobileLinks = document.querySelectorAll('.mobile-nav-link');

    // Function to set active link
    function setActiveLink(links) {
        links.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');

            // Check if current page matches the link
            if (href === '/' && (currentPath === '/' || currentPath === '/index.php' || currentPath.endsWith('/homestay/'))) {
                link.classList.add('active');
            } else if (href !== '/' && currentPath.includes(href.replace('./', ''))) {
                link.classList.add('active');
            }
        });
    }

    setActiveLink(navLinks);
    setActiveLink(mobileLinks);

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            closeMobileMenu();
        }
    });

    // Escape key to close mobile menu
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileNavOverlay && mobileNavOverlay.classList.contains('active')) {
            closeMobileMenu();
        }
    });
});

// Newsletter form functionality
document.addEventListener('DOMContentLoaded', function() {
    const newsletterForm = document.querySelector('.newsletter-form');
    
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const emailInput = this.querySelector('input[type="email"]');
            const email = emailInput.value;
            
            if (email) {
                // Show success message
                const button = this.querySelector('button');
                const originalText = button.textContent;
                button.textContent = 'Subscribed!';
                button.style.background = '#27ae60';
                
                // Reset form
                emailInput.value = '';
                
                // Reset button after 3 seconds
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '';
                }, 3000);
                
                // Here you would typically send the email to your server
                console.log('Newsletter subscription:', email);
            }
        });
    }
});

// Smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            if (href !== '#') {
                const targetElement = document.querySelector(href);
                
                if (targetElement) {
                    e.preventDefault();
                    
                    const headerHeight = document.querySelector('.header').offsetHeight;
                    const targetPosition = targetElement.offsetTop - headerHeight;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });
}); 