* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}



.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 40px;
}

.event-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  max-width: 320px;
  margin: 0 auto;
}

.event-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.event-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.event-status {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
}

.event-content {
  padding: 15px;
}

.event-title {
  font-size: 1.2rem;
  margin-bottom: 8px;
  color: #333;
}

.event-date {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 8px;
}

.countdown {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 10px;
  border-radius: 10px;
  margin-bottom: 12px;
  text-align: center;
}

.countdown-title {
  font-size: 0.8rem;
  margin-bottom: 5px;
  opacity: 0.9;
}

.countdown-timer {
  display: flex;
  justify-content: space-between;
  gap: 5px;
}

.countdown-item {
  flex: 1;
  text-align: center;
}

.countdown-number {
  font-size: 1rem;
  font-weight: bold;
  display: block;
}

.countdown-label {
  font-size: 0.65rem;
  opacity: 0.8;
  text-transform: uppercase;
}

.expired {
  background: linear-gradient(135deg, #666, #444);
}

.expired .countdown-timer {
  justify-content: center;
}

.event-description {
  color: #666;
  line-height: 1.4;
  margin-bottom: 15px;
  font-size: 0.85rem;
}

.whatsapp-btn {
  background: linear-gradient(135deg, #25d366, #128c7e);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: bold;
  cursor: pointer;
  width: 100%;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.whatsapp-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(37, 211, 102, 0.3);
}

.whatsapp-btn:before {
  content: "📱";
  margin-right: 10px;
  font-size: 1rem;
}

.whatsapp-btn:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.whatsapp-btn:hover:after {
  width: 300px;
  height: 300px;
}

.event-card.started .event-status {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.event-card.upcoming .event-status {
  background: linear-gradient(135deg, #4834d4, #686de0);
}

.event-card.soon .event-status {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.event-card.ended .event-status {
  background: linear-gradient(135deg, #666, #444);
}

.event-card.ended {
  opacity: 0.7;
}

.floating-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.floating-elements::before {
  content: "";
  position: absolute;
  top: 10%;
  left: 10%;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.floating-elements::after {
  content: "";
  position: absolute;
  bottom: 20%;
  right: 10%;
  width: 150px;
  height: 150px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.load-more-container {
  text-align: center;
  margin: 30px 0;
}

.load-more-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.load-more-btn.hidden {
  display: none;
}

.event-card.hidden {
  display: none;
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 2rem;
  }

  .events-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .event-card {
    margin: 0 10px;
  }
} 