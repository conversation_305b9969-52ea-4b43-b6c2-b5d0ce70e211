<?php
require_once '../include/connection.php';
$galleries = [];
$sql = "SELECT * FROM gallery ORDER BY created_at DESC";
$result = $conn->query($sql); while ($row = $result->fetch_assoc()) {
$galleries[] = $row; } ?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Modern Gallery</title>
    <link rel="stylesheet" href="../css/rooms.css" />
    <link rel="stylesheet" href="../css/styles.css" />
    <link rel="stylesheet" href="../css/style.css" />
    <link rel="stylesheet" href="../css/gallery.css" />
  </head>
  <body>
    <section class="rooms-hero">
      <h1>Our Rooms</h1>
      <p>Experience comfort and culture in our thoughtfully designed rooms</p>
    </section>
    <div class="gallery-container">
      <div class="album-grid" id="albumGrid">
        <!-- Albums will be generated by JavaScript -->
      </div>
    </div>
    <!-- Lightbox -->
    <div class="lightbox" id="lightbox">
      <div class="lightbox-content">
        <button class="lightbox-close" id="closeLightbox">×</button>
        <button class="lightbox-nav lightbox-prev" id="prevImage">‹</button>
        <button class="lightbox-nav lightbox-next" id="nextImage">›</button>
        <img class="lightbox-image" id="lightboxImage" src="" alt="" />
        <h3 class="lightbox-title" id="lightboxTitle"></h3>
        <div class="lightbox-counter" id="lightboxCounter"></div>
      </div>
    </div>
    <script>
      // Output PHP galleries array as JS variable
      const galleries = <?php echo json_encode($galleries, JSON_HEX_TAG|JSON_HEX_AMP|JSON_HEX_APOS|JSON_HEX_QUOT); ?>;
      let currentAlbumIndex = 0;
      let currentImageIndex = 0;
      let currentImages = [];

      // Shuffle function
      function shuffleArray(array) {
          const shuffled = [...array];
          for (let i = shuffled.length - 1; i > 0; i--) {
              const j = Math.floor(Math.random() * (i + 1));
              [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
          }
          return shuffled;
      }

      // Create gallery cards
      function createGalleryCards() {
          const albumGrid = document.getElementById('albumGrid');
          const shuffledGalleries = shuffleArray(galleries);

          shuffledGalleries.forEach((gallery, galleryIndex) => {
              // Collect all images from DB row
              const allImages = [gallery.img1, gallery.img2, gallery.img3, gallery.img4, gallery.img5, gallery.img6].filter(img => img);
              const cardImage = allImages[0] || '';
              const cardHeights = [220, 260, 320, 180, 240];
              const cardHeight = cardHeights[galleryIndex % cardHeights.length];
              const galleryCard = document.createElement('div');
              galleryCard.className = 'album-card';
              galleryCard.style.minHeight = (cardHeight + 100) + 'px';
              galleryCard.innerHTML = `
                  <div class="album-image" style="height:${cardHeight}px;">
                      <img src="${cardImage}" alt="${gallery.title}" loading="lazy">
                  </div>
                  <h3 class="album-title">${gallery.title}</h3>
              `;

              // Add click event to open lightbox
              galleryCard.addEventListener('click', () => {
                  const imageObjects = allImages.map(src => ({ src, title: gallery.title }));
                  openLightbox(imageObjects, 0);
              });

              albumGrid.appendChild(galleryCard);
          });
      }

      // Lightbox functionality
      function openLightbox(images, startIndex) {
          currentImages = images;
          currentImageIndex = startIndex;

          updateLightboxContent();
          document.getElementById('lightbox').classList.add('active');
          document.body.style.overflow = 'hidden';
      }

      function updateLightboxContent() {
          const currentImage = currentImages[currentImageIndex];
          document.getElementById('lightboxImage').src = currentImage.src;
          document.getElementById('lightboxTitle').textContent = currentImage.title;
          document.getElementById('lightboxCounter').textContent = `${currentImageIndex + 1} / ${currentImages.length}`;
      }

      function closeLightbox() {
          document.getElementById('lightbox').classList.remove('active');
          document.body.style.overflow = 'auto';
      }

      function nextImage() {
          currentImageIndex = (currentImageIndex + 1) % currentImages.length;
          updateLightboxContent();
      }

      function prevImage() {
          currentImageIndex = (currentImageIndex - 1 + currentImages.length) % currentImages.length;
          updateLightboxContent();
      }

      // Event listeners
      document.getElementById('closeLightbox').addEventListener('click', closeLightbox);
      document.getElementById('nextImage').addEventListener('click', nextImage);
      document.getElementById('prevImage').addEventListener('click', prevImage);

      // Keyboard navigation
      document.addEventListener('keydown', (e) => {
          if (!document.getElementById('lightbox').classList.contains('active')) return;

          switch(e.key) {
              case 'Escape':
                  closeLightbox();
                  break;
              case 'ArrowRight':
                  nextImage();
                  break;
              case 'ArrowLeft':
                  prevImage();
                  break;
          }
      });

      // Close lightbox when clicking outside image
      document.getElementById('lightbox').addEventListener('click', (e) => {
          if (e.target.id === 'lightbox') {
              closeLightbox();
          }
      });

      // Initialize gallery
      createGalleryCards();
    </script>
  </body>
</html>
